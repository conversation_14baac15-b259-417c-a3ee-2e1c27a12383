import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  ParseUUIDPipe,
  BadRequestException,
  ParseBoolPipe,
  ParseIntPipe,
} from '@nestjs/common';
import { UsersService } from './users.service';
import { CreateUserDto, UpdateUserDto, UserResponseDto, UserListResponseDto } from './dto/user.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth-guards';
import { RolesGuard } from '../auth/roles.guard';
import { Roles } from '../auth/roles.decorator';
import { CurrentUser } from '../auth/current-user-decorator';

@Controller('api/users')
@UseGuards(JwtAuthGuard)
export class UsersController {
  constructor(private readonly usersService: UsersService) { }

  /**
   * Create new user
   * POST /api/users
   * CompanyAdmin only
   */
  @Post()
  @UseGuards(RolesGuard)
  @Roles('CompanyAdmin')
  async createUser(
    @Body() createUserDto: CreateUserDto,
    @CurrentUser() user: any,
  ): Promise<UserResponseDto> {
    try {
      if (!user.companyId) {
        throw new BadRequestException('User does not have a company assigned');
      }

      // Validate department assignment based on role
      if (createUserDto.role === 'CompanyAdministrator') {
        if (!createUserDto.departmentIds || createUserDto.departmentIds.length === 0) {
          throw new BadRequestException('CompanyAdministrator must be assigned to at least one department');
        }
        if (createUserDto.departmentId) {
          throw new BadRequestException('CompanyAdministrator should use departmentIds, not departmentId');
        }
      } else {
        if (!createUserDto.departmentId) {
          throw new BadRequestException(`${createUserDto.role} must be assigned to a department`);
        }
        if (createUserDto.departmentIds && createUserDto.departmentIds.length > 0) {
          throw new BadRequestException(`${createUserDto.role} should use departmentId, not departmentIds`);
        }
      }

      // Set createdBy from current user
      createUserDto.createdBy = user.sub;

      return await this.usersService.createUser(
        user.companyId,
        createUserDto,
        user.sub,
      );
    } catch (error) {
      throw new BadRequestException(`Failed to create user: ${error.message}`);
    }
  }

  /**
   * Get all users
   * GET /api/users
   * CompanyAdmin can see all, Supervisor can see their team, Employee can see themselves
   */
  @Get()
  async getUsers(
    @CurrentUser() user: any,
    @Query('role') role?: string,
    @Query('department') department?: string,
    @Query('isActive', new ParseBoolPipe({ optional: true })) isActive?: boolean,
    @Query('page', new ParseIntPipe({ optional: true })) page?: number,
    @Query('limit', new ParseIntPipe({ optional: true })) limit?: number,
  ): Promise<UserListResponseDto> {
    try {
      if (!user.companyId) {
        throw new BadRequestException('User does not have a company assigned');
      }

      const filters = {
        role,
        department,
        isActive,
        page,
        limit,
      };

      return await this.usersService.getUsersByCompany(
        user.companyId,
        user.role,
        user.sub,
        filters,
      );


    } catch (error) {
      throw new BadRequestException(`Failed to get users: ${error.message}`);
    }
  }

  /**
   * Get user by ID
   * GET /api/users/:id
   * Role-based access control applied
   */
  @Get(':id')
  async getUserById(
    @Param('id', ParseUUIDPipe) id: string,
    @CurrentUser() user: any,
  ): Promise<UserResponseDto> {
    try {
      if (!user.companyId) {
        throw new BadRequestException('User does not have a company assigned');
      }

      return await this.usersService.getUserById(
        id,
        user.companyId,
        user.role,
        user.sub,
      );
    } catch (error) {
      throw new BadRequestException(`Failed to get user: ${error.message}`);
    }
  }

  /**
   * Update user
   * PUT /api/users/:id
   * CompanyAdmin only
   */
  @Put(':id')
  @UseGuards(RolesGuard)
  @Roles('CompanyAdmin')
  async updateUser(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateUserDto: UpdateUserDto,
    @CurrentUser() user: any,
  ): Promise<UserResponseDto> {
    try {
      console.log('Update User Request - DTO:', JSON.stringify(updateUserDto, null, 2));
      console.log('Update User Request - User ID:', id);
      console.log('Update User Request - Company ID:', user.companyId);

      if (!user.companyId) {
        throw new BadRequestException('User does not have a company assigned');
      }

      // Validate department assignment based on role (only if role or department info is being updated)
      if (updateUserDto.role || updateUserDto.departmentId || updateUserDto.departmentIds) {
        if (updateUserDto.role === 'CompanyAdministrator') {
          if (updateUserDto.departmentIds && updateUserDto.departmentIds.length === 0) {
            throw new BadRequestException('CompanyAdministrator must be assigned to at least one department');
          }
          if (updateUserDto.departmentId) {
            throw new BadRequestException('CompanyAdministrator should use departmentIds, not departmentId');
          }
        } else if (updateUserDto.role && updateUserDto.role !== 'CompanyAdministrator') {
          if (updateUserDto.departmentIds && updateUserDto.departmentIds.length > 0) {
            throw new BadRequestException(`${updateUserDto.role} should use departmentId, not departmentIds`);
          }
        }
      }

      // Set updatedBy from current user
      updateUserDto.updatedBy = user.sub;

      return await this.usersService.updateUser(
        id,
        user.companyId,
        updateUserDto,
        user.sub,
      );
    } catch (error) {
      console.error('Update User Error:', error);

      // Handle Sequelize validation errors
      if (error.name === 'SequelizeValidationError') {
        const validationErrors = error.errors.map((err: any) => `${err.path}: ${err.message}`).join(', ');
        throw new BadRequestException(`Validation failed: ${validationErrors}`);
      }

      // Handle Sequelize unique constraint errors
      if (error.name === 'SequelizeUniqueConstraintError') {
        const constraintName = error.parent?.constraint || error.original?.constraint;

        if (constraintName === 'unique_user_department') {
          throw new BadRequestException('User is already assigned to this department. Please refresh and try again.');
        } else {
          const field = error.errors[0]?.path || 'field';
          const value = error.errors[0]?.value || 'the provided value';
          throw new BadRequestException(`The ${field} '${value}' already exists. Please use a different ${field}.`);
        }
      }

      throw new BadRequestException(`Failed to update user: ${error.message}`);
    }
  }

  /**
   * Delete user
   * DELETE /api/users/:id
   * CompanyAdmin only
   */
  @Delete(':id')
  @UseGuards(RolesGuard)
  @Roles('CompanyAdmin')
  async deleteUser(
    @Param('id', ParseUUIDPipe) id: string,
    @CurrentUser() user: any,
  ): Promise<{ message: string }> {
    try {
      if (!user.companyId) {
        throw new BadRequestException('User does not have a company assigned');
      }

      return await this.usersService.deleteUser(
        id,
        user.companyId,
        user.sub,
      );
    } catch (error) {
      throw new BadRequestException(`Failed to delete user: ${error.message}`);
    }
  }

  /**
   * Get all employees for a specific company
   * GET /api/users/company/:companyId/employees
   * Any authenticated user can access
   */
  @Get('company/:companyId/employees')
  async getCompanyEmployees(
    @Param('companyId', ParseUUIDPipe) companyId: string,
    @Query('isActive', new ParseBoolPipe({ optional: true })) isActive?: boolean,
    @Query('department') department?: string,
    @Query('nameStartsWith') nameStartsWith?: string,
    @Query('page', new ParseIntPipe({ optional: true })) page?: number,
    @Query('limit', new ParseIntPipe({ optional: true })) limit?: number,
  ): Promise<UserListResponseDto> {
    try {
      const filters = {
        isActive,
        department,
        nameStartsWith,
        page,
        limit,
        companyId
      };

      return await this.usersService.getEmployeesByCompany(
        companyId,
        filters,
      );
    } catch (error) {
      throw new BadRequestException(`Failed to get company employees: ${error.message}`);
    }
  }
}
